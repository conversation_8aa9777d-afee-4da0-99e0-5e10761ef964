import { Link } from "@/components/Common/Link";
import { cn } from "@/lib/utils";
import { useState, useEffect } from "react";
import { useAuthStore } from "@/stores/useAuthStore";
import { useUpgradeDialogStore } from "@/stores/useUpgradeDialogStore";
import ProfileEntryV2 from "./ProfileEntryV2";
import FolderList from "./FolderList";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Clock, Zap, House } from "lucide-react";
import { trackAnonymousEvent, trackEvent } from "@/lib/analytics";
import Logo from "@/components/Logo";
import { storageService } from "@/services/storageService";
import { ANONYMOUS_USER_LIMITS } from "@/constants/file";
import { useRouter } from "@/i18n/navigation";

export function Sidebar({ className, isAnonymous, onFolderChange }) {
  const t = useTranslations("dashboard.sidebar");
  const { user } = useAuthStore();
  const { openDialog } = useUpgradeDialogStore();
  const router = useRouter();
  const [anonymousUsage, setAnonymousUsage] = useState({ totalMinutes: 0 });

  // 读取匿名用户使用情况
  useEffect(() => {
    if (isAnonymous) {
      const usage = storageService.getAnonymousTranscriptionCount();
      setAnonymousUsage(usage);
    }
  }, [isAnonymous]);

  const handleSignInClick = () => {
    trackAnonymousEvent("signin_click", {
      source: "sidebar",
    });
  };

  // 处理 Home 点击
  const handleHomeClick = () => {
    // 跳转到 dashboard 主页，和点击 logo 一样的效果
    router.push("/dashboard");
    // 通知父组件重置文件夹选择
    if (onFolderChange) {
      onFolderChange("all");
    }
  };

  // 获取计划显示信息
  const getPlanInfo = () => {
    if (!user) return { planName: "Free", showUpgrade: true };

    const planName = user?.primaryPlan || "Free";

    // 如果是 Free 或 Basic 计划，显示 Upgrade Plan 按钮
    // 如果是 Pro 或 AppSumo 用户，显示 Buy More Minutes 按钮
    const isProOrAppSumo =
      user?.primaryPlan?.toLowerCase().includes("pro") ||
      user?.primaryPlanDetail?.isAppsumo;
    const showUpgrade = !isProOrAppSumo;

    return { planName, showUpgrade };
  };

  // 处理计划按钮点击
  const handlePlanButtonClick = () => {
    const { showUpgrade } = getPlanInfo();

    if (showUpgrade) {
      // 显示升级对话框，默认到 yearly 标签页
      trackEvent("sidebar_plan_click", {
        action: "upgrade",
      });
      openDialog({
        source: "sidebar",
        defaultPlanType: "yearly",
      });
    } else {
      // 显示购买更多分钟对话框，默认到 onetime 标签页
      trackEvent("sidebar_plan_click", {
        action: "buy_more",
      });
      openDialog({
        source: "sidebar",
        defaultPlanType: "onetime",
      });
    }
  };

  return (
    <div
      className={cn(
        "w-full md:w-[260px] border-r border-gray-200 bg-white flex flex-col py-4 h-full",
        className
      )}
    >
      {/* Header */}
      <div className="px-4 flex-shrink-0">
        <div className="mb-5">
          <Logo
            linkProps={{
              href: isAnonymous ? "/" : "/dashboard",
              className: "hover:opacity-90 transition-opacity block",
            }}
          />
        </div>

        {/* Plan Section - More Compact */}
        {!isAnonymous && (
          <div className="bg-[#6366f1]/5 rounded-2xl p-3 border border-[#6366f1]/10 mb-6">
            <div className="flex justify-between items-center mb-3">
              <span className="text-xs font-medium text-gray-600">
                {t("plan.currentPlan")}
              </span>
              <span className="px-2 py-1 bg-green-100 text-green-700 text-xs font-semibold rounded-full">
                {getPlanInfo().planName}
              </span>
            </div>
            <Button
              className="w-full bg-[#6366f1] hover:bg-[#5855eb] text-white font-medium rounded-xl shadow-sm text-sm h-9"
              onClick={handlePlanButtonClick}
            >
              {getPlanInfo().showUpgrade
                ? t("plan.upgradePlan")
                : t("plan.buyMoreMinutes")}
            </Button>
          </div>
        )}

        {/* Navigation Section - Home menu item */}
        {!isAnonymous && (
          <div className="mb-6">
            <div
              className="flex items-center gap-3 p-3 pl-7 rounded-xl cursor-pointer transition-all select-none bg-white hover:bg-gray-50 text-gray-600 hover:text-[#6366f1]"
              onClick={handleHomeClick}
            >
              <House className="w-4 h-4" />
              <span className="font-medium text-sm">{t("home")}</span>
            </div>
          </div>
        )}

        {/* Anonymous User Plan Section */}
        {isAnonymous && (
          <Card className="bg-gradient-to-r from-marketing-50 to-marketing-100 border-marketing-200 mb-6">
            <CardContent className="p-4 space-y-4">
              {/* Header */}
              <div className="flex items-center justify-between">
                <span className="text-xs font-medium text-marketing-800">
                  {t("plan.currentPlan")}
                </span>
                <span className="text-xs font-medium bg-gray-200 text-gray-700 px-2 py-1 rounded-full">
                  {t("plan.guest")}
                </span>
              </div>

              {/* Progress Section */}
              <div className="space-y-3">
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center space-x-1 text-marketing-700">
                    <Clock className="w-3 h-3" />
                    <span>{t("plan.transcription")}</span>
                  </div>
                  <span className="text-marketing-800 font-medium">
                    {anonymousUsage.totalMinutes}/
                    {ANONYMOUS_USER_LIMITS.TRANSCRIPTION_MINUTES} min
                  </span>
                </div>
                <div className="relative">
                  <div className="w-full bg-orange-100 rounded-full h-3 overflow-hidden">
                    <div
                      className="h-full bg-gradient-to-r from-orange-400 to-orange-500 rounded-full transition-all duration-300 ease-out shadow-sm"
                      style={{
                        width: `${
                          (anonymousUsage.totalMinutes /
                            ANONYMOUS_USER_LIMITS.TRANSCRIPTION_MINUTES) *
                          100
                        }%`,
                      }}
                    />
                  </div>
                  {/* Optional: Add a subtle glow effect */}
                  <div
                    className="absolute top-0 h-full bg-gradient-to-r from-orange-300 to-orange-400 rounded-full opacity-50 blur-sm transition-all duration-300"
                    style={{
                      width: `${
                        (anonymousUsage.totalMinutes /
                          ANONYMOUS_USER_LIMITS.TRANSCRIPTION_MINUTES) *
                        100
                      }%`,
                    }}
                  />
                </div>
                <div className="flex items-center space-x-1 text-xs text-marketing-600">
                  <Zap className="w-3 h-3" />
                  <span>{t("plan.signUpForFreeMinutes")}</span>
                </div>
              </div>

              {/* CTA */}
              <div className="py-1.5">
                <Link href="/auth/signup">
                  <Button
                    size="sm"
                    className="w-full bg-marketing-600 hover:bg-marketing-700 text-white"
                    onClick={handleSignInClick}
                  >
                    {t("plan.signUpFree")}
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* FolderList with flex-1 to take available space - Hidden for anonymous users */}
      {!isAnonymous && (
        <div className="flex-1 min-h-0">
          <FolderList
            isAnonymous={isAnonymous}
            className="h-full"
            onFolderChange={onFolderChange}
          />
        </div>
      )}

      {/* Footer - ProfileEntry - Hidden for anonymous users */}
      {!isAnonymous && (
        <div className="px-4 flex-shrink-0">
          <ProfileEntryV2 />
        </div>
      )}
    </div>
  );
}
